/**
 * Lead Status Constants
 * Centralized definition of all lead statuses and their styling
 */

export const LEAD_STATUSES = [
  "ליד חדש",
  "צריך פולוא<PERSON>", 
  "לקוח סגור",
  "לא ענה",
  "לא מעוניין",
  "לא מתאים",
  "פה לאוזן",
  "לקו<PERSON> חוזר",
  "מהרצאה",
  "שיתוף פעולה"
] as const;

export type LeadStatus = typeof LEAD_STATUSES[number];

/**
 * Get the appropriate CSS classes for a lead status badge
 */
export const getLeadStatusColor = (status: string): string => {
  switch (status) {
    case "ליד חדש":
      return "bg-primary text-primary-foreground";
    case "צריך פולואפ":
      return "bg-warning text-warning-foreground";
    case "לקוח סגור":
      return "bg-success text-success-foreground";
    case "לא ענה":
    case "לא מעוניין":
    case "לא מתאים":
      return "bg-muted text-muted-foreground";
    case "פה לאוזן":
      return "bg-blue-500 text-white";
    case "לקוח חוזר":
      return "bg-green-600 text-white";
    case "מהרצאה":
      return "bg-purple-500 text-white";
    case "שיתוף פעולה":
      return "bg-orange-500 text-white";
    default:
      return "bg-muted text-muted-foreground";
  }
};

/**
 * Get the appropriate CSS classes for a lead status in office/leads tab (with transparency)
 */
export const getLeadStatusColorOffice = (status: string): string => {
  switch (status) {
    case "ליד חדש":
      return "bg-primary/10 text-primary border-primary/20";
    case "צריך פולואפ":
      return "bg-warning/10 text-warning border-warning/20";
    case "לקוח סגור":
      return "bg-success/10 text-success border-success/20";
    case "לא ענה":
    case "לא מעוניין":
    case "לא מתאים":
      return "bg-muted text-muted-foreground border-border";
    case "פה לאוזן":
      return "bg-blue-500/10 text-blue-700 border-blue-500/20";
    case "לקוח חוזר":
      return "bg-green-600/10 text-green-700 border-green-600/20";
    case "מהרצאה":
      return "bg-purple-500/10 text-purple-700 border-purple-500/20";
    case "שיתוף פעולה":
      return "bg-orange-500/10 text-orange-700 border-orange-500/20";
    default:
      return "bg-muted text-muted-foreground border-border";
  }
};
